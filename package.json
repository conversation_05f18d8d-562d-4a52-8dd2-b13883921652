{"name": "gemini-blog-platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@google/genai": "^1.11.0", "@lexical/code": "^0.33.1", "@lexical/html": "^0.33.1", "@lexical/link": "^0.33.1", "@lexical/list": "^0.33.1", "@lexical/react": "^0.33.1", "@lexical/rich-text": "^0.33.1", "@lexical/table": "^0.33.1", "@lexical/utils": "^0.33.1", "@supabase/supabase-js": "^2.52.1", "@tailwindcss/vite": "^4.1.11", "@types/prismjs": "^1.26.5", "browser-image-compression": "^2.0.2", "dotenv": "^17.2.1", "highlight.js": "^11.11.1", "lexical": "^0.33.1", "prismjs": "^1.30.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-is": "^19.1.0", "react-markdown": "^10.1.0", "react-router-dom": "^7.7.1", "recharts": "^3.1.0", "remark-gfm": "^4.0.1", "tailwindcss": "^4.1.11", "tinymce": "^7.9.1"}, "devDependencies": {"@types/node": "^22.14.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "typescript": "~5.7.2", "vite": "^6.2.0"}}