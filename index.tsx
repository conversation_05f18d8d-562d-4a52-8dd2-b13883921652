
import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom';
import App from './App';
import { SupabaseBlogProvider } from './context/SupabaseBlogContext';

const rootElement = document.getElementById('root');
if (!rootElement) {
  throw new Error("Could not find root element to mount to");
}

const root = ReactDOM.createRoot(rootElement);

// Conditionally disable StrictMode in development to prevent editor double-render issues
const isDevelopment = import.meta.env.DEV;
const AppWrapper = () => (
  <BrowserRouter>
    <SupabaseBlogProvider>
      <App />
    </SupabaseBlogProvider>
  </BrowserRouter>
);

root.render(
  isDevelopment ? (
    <AppWrapper />
  ) : (
    <React.StrictMode>
      <AppWrapper />
    </React.StrictMode>
  )
);
