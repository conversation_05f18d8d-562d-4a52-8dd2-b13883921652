/* Rich Text Editor Styles */
.rich-text-editor {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  transition: border-color 0.2s ease;
}

.rich-text-editor:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Lexical Editor Container */
.lexical-editor-container {
  position: relative;
  background: white;
  border-radius: 8px;
}

/* Lexical Content Editable */
.lexical-content-editable {
  border: none;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-size: 14px;
  line-height: 1.6;
  resize: none;
  cursor: text;
  tab-size: 1;
}

.lexical-content-editable:focus {
  outline: none;
}

/* Placeholder */
.lexical-placeholder {
  color: #9ca3af;
  overflow: hidden;
  position: absolute;
  text-overflow: ellipsis;
  top: 16px;
  left: 16px;
  font-size: 14px;
  user-select: none;
  display: inline-block;
  pointer-events: none;
}

/* Lexical Toolbar */
.lexical-toolbar {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
  flex-wrap: wrap;
}

.lexical-toolbar-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  background: transparent;
  color: #374151;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.lexical-toolbar-button:hover {
  background-color: #e2e8f0;
}

.lexical-toolbar-button:active {
  background-color: #d1d5db;
}

.lexical-toolbar-divider {
  width: 1px;
  height: 24px;
  background-color: #d1d5db;
  margin: 0 4px;
}

/* Lexical Text Styles */
.lexical-text-bold {
  font-weight: bold;
}

.lexical-text-italic {
  font-style: italic;
}

.lexical-text-underline {
  text-decoration: underline;
}

.lexical-text-strikethrough {
  text-decoration: line-through;
}

.lexical-text-code {
  background-color: #f3f4f6;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
}

/* Lexical Heading Styles */
.lexical-heading-h1 {
  font-size: 2em;
  font-weight: 700;
  margin: 1em 0 0.5em 0;
  line-height: 1.2;
}

.lexical-heading-h2 {
  font-size: 1.5em;
  font-weight: 600;
  margin: 1em 0 0.5em 0;
  line-height: 1.3;
}

.lexical-heading-h3 {
  font-size: 1.25em;
  font-weight: 600;
  margin: 1em 0 0.5em 0;
  line-height: 1.4;
}

.lexical-heading-h4,
.lexical-heading-h5,
.lexical-heading-h6 {
  font-weight: 600;
  margin: 1em 0 0.5em 0;
  line-height: 1.4;
}

/* Lexical Quote Styles */
.lexical-quote {
  border-left: 4px solid #3b82f6;
  padding-left: 16px;
  margin: 16px 0;
  color: #6b7280;
  font-style: italic;
}

/* Lexical Code Styles */
.lexical-code {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 16px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 16px 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* Lexical List Styles */
.lexical-list-ol,
.lexical-list-ul {
  margin: 16px 0;
  padding-left: 24px;
}

.lexical-listitem {
  margin: 4px 0;
}

.lexical-nested-listitem {
  list-style-type: none;
}

/* Lexical Link Styles */
.lexical-link {
  color: #3b82f6;
  text-decoration: underline;
}

.lexical-link:hover {
  color: #1d4ed8;
}

/* Lexical Paragraph */
.lexical-paragraph {
  margin: 0 0 16px 0;
}</anical:parameter>
<parameter name="old_str_start_line_number">106

/* Disabled State */
.rich-text-editor .ql-container.ql-disabled .ql-editor {
  background-color: #f9fafb;
  color: #6b7280;
}

.rich-text-editor .ql-toolbar.ql-disabled {
  background-color: #f3f4f6;
}

.rich-text-editor .ql-toolbar.ql-disabled button,
.rich-text-editor .ql-toolbar.ql-disabled .ql-picker {
  opacity: 0.5;
  pointer-events: none;
}

/* Focus States */
.rich-text-editor .ql-editor:focus {
  outline: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .rich-text-editor .ql-toolbar {
    padding: 6px 8px;
  }
  
  .rich-text-editor .ql-toolbar .ql-formats {
    margin-right: 8px;
  }
  
  .rich-text-editor .ql-editor {
    padding: 12px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .rich-text-editor {
    border-color: #374151;
    background: #1f2937;
  }
  
  .rich-text-editor .ql-toolbar {
    background: #111827;
    border-bottom-color: #374151;
  }
  
  .rich-text-editor .ql-container {
    background: #1f2937;
    color: #f9fafb;
  }
  
  .rich-text-editor .ql-editor.ql-blank::before {
    color: #6b7280;
  }
}
