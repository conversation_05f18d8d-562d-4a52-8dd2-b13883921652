<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Editor</title>
</head>
<body>
    <h1>Test Editor Page</h1>
    <p>Open the browser console and navigate to the post editor to check for infinite re-rendering.</p>
    <p>If you see repeated "PostEditorPage rendering" and "handleContentChange called" messages, the issue persists.</p>
    <p>If you see only a few initial renders, the issue is fixed.</p>
    
    <a href="http://localhost:5176/admin/posts/new" target="_blank">Open Post Editor</a>
    
    <script>
        console.log('Test page loaded. Open the post editor link above and check the console for re-rendering issues.');
    </script>
</body>
</html>
